package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.ClienteRestricaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.restricao.EnvelopeRespostaClienteRestricaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaBoolan;
import com.pacto.adm.core.dto.enveloperesposta.cliente.restricao.EnvelopeRespostaListClienteRestricaoDTO;
import com.pacto.adm.core.dto.filtros.FiltroClienteRestricaoJSON;
import com.pacto.adm.core.enumerador.clienterestricao.TipoClienteRestricaoEnum;
import com.pacto.adm.core.services.interfaces.ClienteRestricaoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/cliente-restricao")
@Tag(name = "Clientes")
public class ClienteRestricaoController {

    private final ClienteRestricaoService clienteRestricaoService;

    public ClienteRestricaoController(ClienteRestricaoService clienteRestricaoService) {
        this.clienteRestricaoService = clienteRestricaoService;
    }

    @Operation(
            summary = "Consultar clientes com restrições",
            description = "Consulta os clientes com restrições na academia, podendo realizar filtros durante a busca.",
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa que será consultada", required = true, example = "1", in = ParameterIn.HEADER),
                    @Parameter(
                            name = "filters",
                            description = "<br/><strong>Filtro disponível</strong>" +
                                    "<ul><li><strong>quicksearchValue:</strong> Termo utilizado para filtrar registros pelo nome ou CPF do cliente.</li></ul>",
                            example = "{\"quicksearchValue\":\"Roberto\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "sort", description = "Ordena as respostas por um atributo da resposta.<br/> " +
                            "<strong>Ordens disponíveis</strong>" +
                            "<ul>" +
                            "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                            "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                            "</ul>" +
                            "Por padrão, caso não seja informado nenhum valor, as respostas serão ordenadas pelo código da restrição",
                            example = "nome,asc", schema = @Schema(implementation = String.class))
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListClienteRestricaoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListClienteRestricaoDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("")
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                       @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroClienteRestricaoJSON filtrosJSON = new FiltroClienteRestricaoJSON(filtros);
            return ResponseEntityFactory.ok(clienteRestricaoService.findAll(filtrosJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar as restrições de um cliente pelo CPF",
            description = "Consulta as restrições de um cliente buscando pelo CPF.",
            parameters = {
                    @Parameter(
                            name = "cpf",
                            description = "CPF do cliente que se deseja consultar as restrições",
                            example = "455.123.234-10",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListClienteRestricaoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListClienteRestricaoDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("/{cpf}")
    public ResponseEntity<EnvelopeRespostaDTO> findByCpf(@PathVariable(name = "cpf") String cpf) throws Exception {
        return ResponseEntityFactory.ok(clienteRestricaoService.findByCpf(cpf));
    }


    @Operation(
            summary = "Consultar as restrições de um cliente pelo código da matrícula",
            description = "Consulta as restrições de um cliente buscando pelo código da matrícula dele.",
            parameters = {
                    @Parameter(
                            name = "codigoMatricula",
                            description = "Código da matrícula do cliente que se deseja consultar as restrições",
                            example = "234",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListClienteRestricaoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListClienteRestricaoDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("/matricula/{codigoMatricula}")
    public ResponseEntity<EnvelopeRespostaDTO> findByCodigoMatricula(@PathVariable(name = "codigoMatricula") Integer codigoMatricula) throws Exception {
        return ResponseEntityFactory.ok(clienteRestricaoService.findByCodigoMatricula(codigoMatricula));
    }

    @Operation(
            summary = "Criar restrição para um cliente",
            description = "Cria uma restrição para um cliente. Deve-se remover do corpo da requisição o atributo código.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar um registro",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ClienteRestricaoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaClienteRestricaoDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = @ExampleObject(name = "Resposta 200 (Sem corpo de resposta)", value = "")
                            )
                    )
            }
    )
    @PostMapping("")
    public ResponseEntity<EnvelopeRespostaDTO> create(@RequestBody ClienteRestricaoDTO clienteRestricaoDTO) {
        try {
            clienteRestricaoService.create(clienteRestricaoDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Operation(
            summary = "Excluir restrição de um cliente",
            description = "Exclui a restrição de um cliente.",
            parameters = {
                    @Parameter(
                            name = "cpf",
                            description = "CPF do cliente que terá a restrição excluída",
                            example = "455.123.234-10",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "chaveEmpresa",
                            description = "Chave da empresa que o cliente possui a restrição",
                            example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "codigoEmpresa",
                            description = "Codigo da empresa que o cliente possui a restrição",
                            example = "1",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "tipoRestricao",
                            description = "Tipo da restrição que o cliente o cliente possui",
                            example = "INADINPLENCIA",
                            schema = @Schema(implementation = TipoClienteRestricaoEnum.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = @ExampleObject(name = "Resposta 200 (Sem corpo de resposta)", value = "")
                            )
                    )
            }
    )
    @DeleteMapping("/{cpf}/{chaveEmpresa}/{codigoEmpresa}/{tipoRestricao}")
    public ResponseEntity<EnvelopeRespostaDTO> delete(@PathVariable(value = "cpf") String cpf,
                                                      @PathVariable(value = "chaveEmpresa") String chaveEmpresa,
                                                      @PathVariable(value = "codigoEmpresa") Integer codigoEmpresa,
                                                      @PathVariable(value = "tipoRestricao") String tipoRestricao) {
        try {
            clienteRestricaoService.delete(cpf, chaveEmpresa, codigoEmpresa, tipoRestricao);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(
            summary = "Excluir restrições de vários clientes",
            description = "Exclui restrições de vários clientes.",
            requestBody =  @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Lista com os CPFs dos clientes que terão as restrições excluídas",
                    required = true,
                    content = @Content(
                            array = @ArraySchema(
                                    schema = @Schema(
                                            type = "string",
                                            description = "CPF dos clientes",
                                            example = "455.123.234-10"
                                    )
                            )
                    )
            ),
            parameters = {
                    @Parameter(
                            name = "chaveEmpresa",
                            description = "Chave da empresa que está cadastrada as restrições",
                            example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "codigoEmpresa",
                            description = "Codigo da empresa que o cliente possui a restrição",
                            example = "1",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "tipoRestricao",
                            description = "Tipo da restrição que os clientes possuem",
                            example = "INADINPLENCIA",
                            schema = @Schema(implementation = TipoClienteRestricaoEnum.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida."
                    ),
                   
            }
    )
    @PostMapping("/{chaveEmpresa}/{codigoEmpresa}/{tipoRestricao}")
    public ResponseEntity<EnvelopeRespostaDTO> delete(@PathVariable(value = "chaveEmpresa") String chaveEmpresa,
                                                      @PathVariable(value = "codigoEmpresa") Integer codigoEmpresa,
                                                      @PathVariable(value = "tipoRestricao") String tipoRestricao,
                                                      @RequestBody List<String> cpfs) {
        try {
            clienteRestricaoService.delete(cpfs, chaveEmpresa, codigoEmpresa, tipoRestricao);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Operation(
            summary = "Atualiza as lista restrições de inadimplencia de um cliente",
            description = "Atualiza as lista restrições de inadimplencia de um cliente. Deve-se passar o cpf.",
            parameters = {
                    @Parameter(
                            name = "cpf",
                            description = "CPF do cliente",
                            example = "455.123.234-10",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = @ExampleObject(name = "Resposta 200 (Sem corpo de resposta)", value = "")
                            )
                    )
            }
    )
    @PostMapping("/{cpf}/atualizar")
    public ResponseEntity<EnvelopeRespostaDTO> atualizarRestricoesInadimplencia(@PathVariable String cpf) {
        try {
            clienteRestricaoService.atualizarRestricoesDeInadimplenciaSync(cpf);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
