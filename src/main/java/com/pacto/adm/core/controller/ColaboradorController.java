package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.colaborador.EnvelopeRespostaColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.colaborador.EnvelopeRespostaListColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.colaborador.EnvelopeRespostaTipoColaboradorString;
import com.pacto.adm.core.dto.filtros.FiltroColaboradorJSON;
import com.pacto.adm.core.services.interfaces.ColaboradorService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/colaboradores")
@Tag(name = "Colaborador")
public class ColaboradorController {

    @Autowired
    private ColaboradorService colaboradorService;

    @Operation(
            summary = "Consultar colaborador pelo código dele",
            description = "Consulta as informações de um colaborador pelo código dele.",
            parameters = {
                    @Parameter(name = "codigo", description = "Código do colaborador", example = "3", required = true)
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaColaboradorDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaColaboradorDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("/{codigo}")
    public ResponseEntity<EnvelopeRespostaDTO> obter(@PathVariable Integer codigo) {
        try {
            return ResponseEntityFactory.ok(colaboradorService.findByCodigo(codigo));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Operation(
            summary = "Consultar tipo do colaborador",
            description = "Consulta o tipo do colaborador pelo código dele ou pelo código de usuário dele.",
            parameters = {
                    @Parameter(name = "codigo", description = "Código do colaborador", example = "3", required = true),
                    @Parameter(name = "usuario", description = "Código do usuário do colaborador.", example = "23")
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaTipoColaboradorString.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaTipoColaboradorString.respostaString)}
                            )
                    ),
            }
    )
    @GetMapping("/{codigo}/tipo")
    public ResponseEntity<EnvelopeRespostaDTO> tiposColaborador(@PathVariable Integer codigo,
                                                                @RequestParam(required = false) Integer usuario) {
        try {
            return ResponseEntityFactory.ok(colaboradorService.tiposColaborador(codigo, usuario));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Operation(
            summary = "Consultar colabores ativos",
            description = "Consulta colaboradores que estão ativos.",
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa que os colaboradores serão consultados",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListColaboradorDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListColaboradorDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("/ativos")
    public ResponseEntity<EnvelopeRespostaDTO> findAll() {
        try {
            return ResponseEntityFactory.ok(colaboradorService.findAllColaboradoresAtivos());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Consultar colabores ativos por vínculo com paginação",
            description = "Consulta colaboradores que estão ativos pelo tipo de vínculo. As respostas serão paginadas",
            parameters = {
                    @Parameter(name = "tipoVinculo", description = "Tipo de vínculo do colaborador", example = "CO", required = true),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca. Deve ser informado como um JSON<br/>" +
                                    "<br/><strong>Filtros disponíveis:</strong>" +
                                    "<ul>" +
                                    "<li><strong>quickSearchValue:</strong> Filtra pelo nome do cliente.</li>" +
                                    "<li><strong>empresa:</strong> Código da empresa que o colaborador está vinculado.</li>" +
                                    "</ul>",
                            example = "{\"quickSearchValue\": \"Ana\",\n\"empresa\": 1\n}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListColaboradorDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListColaboradorDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("/ativos/{tipoVinculo}")
    public ResponseEntity<EnvelopeRespostaDTO> pageAtivosPorTipoVinculo(
            @PathVariable String tipoVinculo,
            @RequestParam(required = false) String filters,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroColaboradorJSON filtroColaboradorJSON = new FiltroColaboradorJSON();
            if (filters != null) {
                filtroColaboradorJSON = new FiltroColaboradorJSON(new JSONObject(filters));
            }
            List<ColaboradorDTO> colaboradorDTO = colaboradorService.findAllAtivosPorTipoVinculo(
                    tipoVinculo, false, filtroColaboradorJSON, paginadorDTO
            );
            return ResponseEntityFactory.ok(colaboradorDTO, paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Operation(
            summary = "Consultar colabores ativos por vínculo sem paginação",
            description = "Consulta colaboradores que estão ativos pelo tipo de vínculo. As respostas não são paginadas",
            parameters = {
                    @Parameter(name = "tipoVinculo", description = "Tipo de vínculo do colaborador", example = "CO", required = true),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca. Deve ser informado como um JSON<br/>" +
                                    "<br/><strong>Filtros disponíveis:</strong>" +
                                    "<ul>" +
                                    "<li><strong>quickSearchValue:</strong> Filtra pelo nome do cliente.</li>" +
                                    "<li><strong>empresa:</strong> Código da empresa que o colaborador está vinculado.</li>" +
                                    "</ul>",
                            example = "{\"quickSearchValue\": \"Ana\",\n\"empresa\": 1\n}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListColaboradorDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListColaboradorDTO.resposta)}
                            )
                    ),

            }
    )
    @GetMapping("/ativos/{tipoVinculo}/full")
    public ResponseEntity<EnvelopeRespostaDTO> findAllAtivosPorTipoVinculo(
            @PathVariable String tipoVinculo,
            @RequestParam(required = false) String filters
    ) {
        try {
            FiltroColaboradorJSON filtroColaboradorJSON = new FiltroColaboradorJSON();
            if (filters != null) {
                filtroColaboradorJSON = new FiltroColaboradorJSON(new JSONObject(filters));
            }
            List<ColaboradorDTO> colaboradorDTO = colaboradorService.findAllAtivosPorTipoVinculo(
                    tipoVinculo, true, filtroColaboradorJSON, null
            );
            return ResponseEntityFactory.ok(colaboradorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Operation(
            summary = "Consultar consultores ativos",
            description = "Consulta colaboradores do tipo consultor que estão ativos.",
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListColaboradorDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListColaboradorDTO.resposta)}
                            )
                    ),

            }
    )
    @GetMapping("/consultores-ativos")
    public ResponseEntity<EnvelopeRespostaDTO> findConsultoresAtivos() {
        try {
            return ResponseEntityFactory.ok(colaboradorService.findAllConsultoresAtivos());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Operation(
            summary = "Consultar professores ativos",
            description = "Consulta colaboradores do tipo professor que estão ativos.",
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListColaboradorDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListColaboradorDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("/professores-ativos")
    public ResponseEntity<EnvelopeRespostaDTO> findProfessoresAtivos() {
        try {
            return ResponseEntityFactory.ok(colaboradorService.findAllProfessoresAtivos());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
